// MainActivity.kt
package com.foisx.recycleview_animtion

import android.os.Bundle
import android.util.Log
import android.widget.Button
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.foisx.recycleview_animtion.adapter.ChatAdapter
import com.foisx.recycleview_animtion.manager.ChatLayoutManager
import com.foisx.recycleview_animtion.model.ChatMessage
import com.foisx.recycleview_animtion.model.MessageType

class MainActivity : AppCompatActivity() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var btnAddEntrance: Button
    private lateinit var btnAddTenEntrance: Button
    private lateinit var btnAddSpeech: Button
    private lateinit var adapter: ChatAdapter
    private lateinit var chatLayoutManager: ChatLayoutManager

    // 模拟数据
    private val usernames = listOf(
        "小明", "小红", "小刚", "小丽", "小华", "小强", "小美", "小亮",
        "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十"
    )

    private val speechContents = listOf(
        "大家好！", "今天天气真不错", "有人在吗？", "这个直播间很有趣",
        "主播加油！", "666", "哈哈哈", "太厉害了", "学到了很多",
        "感谢分享", "继续继续", "支持支持", "很棒的内容"
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)
        setupWindowInsets()
        initViews()
        setupRecyclerView()
        setupClickListeners()
    }

    private fun initViews() {
        recyclerView = findViewById(R.id.recyclerView)
        btnAddEntrance = findViewById(R.id.btnAddEntrance)
        btnAddTenEntrance = findViewById(R.id.btnAddTenEntrance)
        btnAddSpeech = findViewById(R.id.btnAddSpeech)
    }

    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }

    private fun setupRecyclerView() {
        adapter = ChatAdapter()

        recyclerView.apply {
            layoutManager = LinearLayoutManager(this@MainActivity, LinearLayoutManager.VERTICAL, true)
            adapter = <EMAIL>
            itemAnimator = null // 禁用默认动画
        }

        // 使用完整的架构，传递队列大小变化回调
        chatLayoutManager = ChatLayoutManager(recyclerView, adapter) {
            // 队列大小变化时更新UI
            updateStatusInfo()
        }
    }

    private fun setupClickListeners() {
        btnAddEntrance.setOnClickListener {
            addEntranceMessage()
        }

        btnAddTenEntrance.setOnClickListener {
            addTenEntranceMessages()
        }

        // 长按清除队列
        btnAddTenEntrance.setOnLongClickListener {
            clearAllQueues()
            true
        }

        btnAddSpeech.setOnClickListener {
            addSpeechMessage()
        }
    }

    /**
     * 清除所有队列，用于调试和紧急情况
     */
    private fun clearAllQueues() {
        chatLayoutManager.getTransitionManager().clearQueue()
        chatLayoutManager.getMessageDispatcher().clearQueue()
        updateStatusInfo()
    }

    private fun addEntranceMessage() {
        val randomUser = usernames.random()
        val entranceMessage = ChatMessage(
            type = MessageType.ENTRANCE,
            content = "$randomUser 进入了房间",
            username = randomUser
        )

        chatLayoutManager.addMessage(entranceMessage)
        updateStatusInfo()
    }

    /**
     * 一次性添加十条进场消息
     * 使用不同的用户名来模拟多个用户同时进入房间的场景
     */
    private fun addTenEntranceMessages() {
        // 创建十条不同用户的进场消息
        repeat(100) { index ->
            val randomUser = usernames.random()
            val entranceMessage = ChatMessage(
                type = MessageType.ENTRANCE,
                content = "$randomUser 进入了房间",
                username = randomUser
            )

            // 添加消息到聊天管理器
            chatLayoutManager.addMessage(entranceMessage)
        }

        // 更新状态信息
        updateStatusInfo()
    }

    private fun addSpeechMessage() {
        val randomUser = usernames.random()
        val randomContent = speechContents.random()

        val speechMessage = ChatMessage(
            type = MessageType.SPEECH,
            content = randomContent,
            username = randomUser
        )

        chatLayoutManager.addMessage(speechMessage)
        updateStatusInfo()
    }
    // MainActivity.kt - 更新状态显示部分
    private fun updateStatusInfo() {


        // 只在真正的过渡动画期间禁用按钮，而不是在消息处理期间

        Log.d("MainActivity", "状态更新 - 状态:$state, 发言队列:$speechQueueSize, 进场队列:$entranceQueueSize, 总数:$messageCount")
    }
    private fun updateStatusInfo() {
        val state = chatLayoutManager.getCurrentState()
        val speechQueueSize = chatLayoutManager.getSpeechQueueSize()
        val entranceQueueSize = chatLayoutManager.getEntranceQueueSize()
        val messageCount = adapter.getMessageCount()
        val isTransitioning = chatLayoutManager.isInTransition()

        btnAddEntrance.text = "添加进场消息\n状态:$state\n进场队列:$entranceQueueSize"
        btnAddTenEntrance.text = "添加十条进场息\n状态:$state\n进场队列:$entranceQueueSize"
        btnAddSpeech.text = "添加发言消息\n总数:$messageCount\n发言队列:$speechQueueSize"

        // 过渡期间禁用按钮
        val shouldDisable = isTransitioning && chatLayoutManager.getTransitionManager().isInTransition()
        btnAddEntrance.isEnabled = !shouldDisable
        btnAddSpeech.isEnabled = !shouldDisable
        btnAddTenEntrance.isEnabled = !shouldDisable

        if (isTransitioning) {
            recyclerView.postDelayed({
                btnAddEntrance.isEnabled = true
                btnAddTenEntrance.isEnabled = true
                btnAddSpeech.isEnabled = true
            }, 1000)
        }
    }
}


