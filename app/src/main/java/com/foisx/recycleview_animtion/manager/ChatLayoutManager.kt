// ChatLayoutManager.kt - 改进版
package com.foisx.recycleview_animtion.manager

import android.util.Log
import androidx.recyclerview.widget.RecyclerView
import com.foisx.recycleview_animtion.adapter.ChatAdapter
import com.foisx.recycleview_animtion.model.ChatMessage
import com.foisx.recycleview_animtion.model.MessageType

class ChatLayoutManager(
    private val recyclerView: RecyclerView,
    private val adapter: ChatAdapter
) {
    private var layoutState = LayoutState.UNIFIED
    private val messageDispatcher = SmartMessageDispatcher(this, adapter)
    private val transitionManager = RobustTransitionManager(recyclerView, adapter)
    private val testManager = TestableLayoutManager()

    enum class LayoutState {
        UNIFIED,        // 前5条统一管理
        SEPARATED,      // 分层管理
        TRANSITIONING   // 过渡中
    }

    fun addMessage(message: ChatMessage) {
        Log.d("ChatLayoutManager", "添加消息: ${message.type}, 当前状态: $layoutState, 总数: ${adapter.getMessageCount()}")
        messageDispatcher.dispatchMessage(message)
    }

    fun handleUnifiedMode(message: ChatMessage) {
        adapter.addMessage(message)
        scrollToTop()

        // 检查是否需要切换状态
        if (adapter.getMessageCount() > 5 && layoutState == LayoutState.UNIFIED) {
            triggerTransitionToSeparated()
        }
    }

    fun handleSeparatedMode(message: ChatMessage) {
        when (message.type) {
            MessageType.ENTRANCE -> handleEntranceInSeparated(message)
            MessageType.SPEECH -> handleSpeechInSeparated(message)
        }
    }

    private fun handleEntranceInSeparated(message: ChatMessage) {
        val firstMessage = adapter.getFirstMessage()
        if (firstMessage?.type == MessageType.ENTRANCE) {
            // 第一条是进场消息，执行替换动画
            transitionManager.executeEntranceReplacement(message)
        } else {
            // 第一条不是进场消息，正常添加到顶部
            adapter.addMessage(message)
            scrollToTop()
        }
    }

    private fun handleSpeechInSeparated(message: ChatMessage) {
        Log.d("ChatLayoutManager", "在分离模式下处理发言消息: ${message.content}")

        // 发言消息的处理逻辑
        if (adapter.getMessageCount() > 0) {
            // 如果第一条是进场消息，发言消息从第二位插入
            val firstMessage = adapter.getFirstMessage()
            if (firstMessage?.type == MessageType.ENTRANCE) {
                adapter.insertMessageAt(1, message)
                Log.d("ChatLayoutManager", "发言消息插入到第二位")
            } else {
                // 如果第一条不是进场消息，正常添加到顶部
                adapter.addMessage(message)
                Log.d("ChatLayoutManager", "发言消息添加到顶部")
            }
        } else {
            // 如果没有消息，直接添加
            adapter.addMessage(message)
            Log.d("ChatLayoutManager", "发言消息添加到空列表")
        }

        scrollToTop()
    }

    private fun triggerTransitionToSeparated() {
        if (layoutState != LayoutState.TRANSITIONING) {
            Log.d("ChatLayoutManager", "开始切换到分离模式")
            layoutState = LayoutState.TRANSITIONING

            transitionManager.executeLayoutTransition {
                layoutState = LayoutState.SEPARATED
                Log.d("ChatLayoutManager", "成功切换到分离模式")
            }
        }
    }

    private fun scrollToTop() {
        recyclerView.post {
            if (adapter.getMessageCount() > 0) {
                recyclerView.smoothScrollToPosition(0)
            }
        }
    }

    // 公开接口
    fun getCurrentState(): LayoutState = layoutState
    fun isInTransition(): Boolean = layoutState == LayoutState.TRANSITIONING

    // 获取队列信息
    fun getSpeechQueueSize(): Int = messageDispatcher.getSpeechQueueSize()
    fun getEntranceQueueSize(): Int = messageDispatcher.getEntranceQueueSize()

    // 测试接口
    fun getTestManager(): TestableLayoutManager = testManager
    fun getTransitionManager(): RobustTransitionManager = transitionManager
    fun getMessageDispatcher(): SmartMessageDispatcher = messageDispatcher
}
